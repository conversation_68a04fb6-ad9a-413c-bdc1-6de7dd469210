import { BasicColumn } from '/@/components/Table';
import { FormSchema } from '/@/components/Table';
import { rules } from '/@/utils/helper/validator';
import { render } from '/@/utils/common/renderUtils';

export const columns: BasicColumn[] = [
  {
    title: '保单号',
    dataIndex: 'policyNo',
    width: 150,
  },
  {
    title: '公司编码',
    dataIndex: 'companyCode',
    width: 120,
    customRender: ({ text }) => {
      return render.renderDict(text, 'supplier');
    },
  },
  {
    title: '保单生效日期',
    dataIndex: 'inforceDate',
    width: 120,
    customRender: ({ text }) => {
      return render.renderDate(text);
    },
  },
  {
    title: '产品英文名称',
    dataIndex: 'planName',
    width: 180,
  },
  {
    title: '佣金期数',
    dataIndex: 'yearNum',
    width: 100,
  },
  {
    title: '缴费币种',
    dataIndex: 'paymentCurrency',
    width: 100,
  },
  {
    title: '保费金额',
    dataIndex: 'premiumAmount',
    width: 120,
    customRender: ({ text }) => {
      return render.renderMoney(text);
    },
  },
  {
    title: '佣金金额',
    dataIndex: 'commissionAmount',
    width: 120,
    customRender: ({ text }) => {
      return render.renderMoney(text);
    },
  },
  {
    title: '导入时间',
    dataIndex: 'createTime',
    width: 150,
    customRender: ({ text }) => {
      return render.renderDate(text, 'YYYY-MM-DD HH:mm:ss');
    },
  },
];

export const searchFormSchema: FormSchema[] = [
  {
    label: '公司编码',
    field: 'companyCode',
    component: 'JDictSelectTag',
    componentProps: {
      dictCode: 'supplier',
      placeholder: '请选择公司',
    },
    colProps: { span: 6 },
  },
  {
    label: '保单号',
    field: 'policyNo',
    component: 'Input',
    componentProps: {
      placeholder: '请输入保单号',
    },
    colProps: { span: 6 },
  },
  {
    label: '产品英文名称',
    field: 'planName',
    component: 'Input',
    componentProps: {
      placeholder: '请输入产品英文名称',
    },
    colProps: { span: 6 },
  },
  {
    label: '佣金期数',
    field: 'yearNum',
    component: 'InputNumber',
    componentProps: {
      placeholder: '请输入佣金期数',
      min: 1,
    },
    colProps: { span: 6 },
  },
];

export const formSchema: FormSchema[] = [
  // TODO 主键隐藏字段，目前写死为ID
  { label: '', field: 'id', component: 'Input', show: false },
  {
    label: '保单号',
    field: 'policyNo',
    component: 'Input',
    required: true,
  },
  {
    label: '公司编码',
    field: 'companyCode',
    component: 'JDictSelectTag',
    componentProps: {
      dictCode: 'supplier',
    },
    required: true,
  },
  {
    label: '保单生效日期',
    field: 'inforceDate',
    component: 'DatePicker',
    componentProps: {
      format: 'YYYY-MM-DD',
      valueFormat: 'YYYY-MM-DD',
    },
  },
  {
    label: '产品英文名称',
    field: 'planName',
    component: 'Input',
  },
  {
    label: '佣金期数',
    field: 'yearNum',
    component: 'InputNumber',
    componentProps: {
      min: 1,
    },
  },
  {
    label: '缴费币种',
    field: 'paymentCurrency',
    component: 'Input',
  },
  {
    label: '保费金额',
    field: 'premiumAmount',
    component: 'InputNumber',
    componentProps: {
      precision: 2,
      min: 0,
    },
  },
  {
    label: '佣金金额',
    field: 'commissionAmount',
    component: 'InputNumber',
    componentProps: {
      precision: 2,
      min: 0,
    },
  },
];
