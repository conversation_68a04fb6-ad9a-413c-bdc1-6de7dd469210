package org.jeecg.modules.crmfy.crmcommissionbill.mapper;

import java.util.List;

import org.apache.ibatis.annotations.Param;
import org.jeecg.modules.crmfy.crmcommissionbill.entity.CrmCommissionBill;
import org.jeecg.modules.crmfy.crmcommissionbill.vo.CrmCommissionBillPO;
import org.jeecg.modules.crmfy.crmcommissionbill.vo.CrmCommissionBillVO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;

/**
 * @Description: 佣金账单
 * @Author: jeecg-boot
 * @Date:   2025-04-11
 * @Version: V1.0
 */
public interface CrmCommissionBillMapper extends BaseMapper<CrmCommissionBill> {

    /**
     * 分页查询佣金账单对账信息
     * @param page 分页参数
     * @param crmCommissionBillPO 查询条件
     * @return 分页数据
     */
    IPage<CrmCommissionBillVO> queryBillCompare(Page<CrmCommissionBillVO> page, @Param("po") CrmCommissionBillPO crmCommissionBillPO);

}
