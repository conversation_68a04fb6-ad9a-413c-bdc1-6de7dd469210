package org.jeecg.modules.crmfy.crmcommissionbill.vo;

import lombok.Data;
import java.math.BigDecimal;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import io.swagger.annotations.ApiModelProperty;

@Data
public class CrmCommissionBillVO implements java.io.Serializable {
    private static final long serialVersionUID = 1L;

    // 保单基本信息
    @ApiModelProperty(value = "保单号")
    private String policyNo;    // 保单号

    @ApiModelProperty(value = "公司编码")
    private String companyCode; // 公司编码

    @ApiModelProperty(value = "产品英文名称")
    private String planName;    // 产品英文名称

    @ApiModelProperty(value = "佣金期数")
    private Integer yearNum;    // 佣金期数

    @ApiModelProperty(value = "缴费币种")
    private String paymentCurrency; // 缴费币种

    // 公司台账信息 (来自crm_commission_calculation)
    @ApiModelProperty(value = "台账保费金额")
    private BigDecimal ledgerPremiumAmount;  // 台账保费金额

    @ApiModelProperty(value = "台账佣金金额")
    private BigDecimal ledgerCommissionAmount; // 台账佣金金额

    @ApiModelProperty(value = "台账计算日期")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date ledgerCalculationDate; // 台账计算日期

    // 保险公司账单信息 (来自crm_commission_bill)
    @ApiModelProperty(value = "保单生效日期")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date inforceDate;   // 保单生效日期

    @ApiModelProperty(value = "账单保费金额")
    private BigDecimal billPremiumAmount;    // 账单保费金额

    @ApiModelProperty(value = "账单佣金金额")
    private BigDecimal billCommissionAmount; // 账单佣金金额

    @ApiModelProperty(value = "导入时间")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date importTime;    // 导入时间

    // 对账状态
    @ApiModelProperty(value = "对账状态")
    private String reconcileStatus; // 对账状态 (MATCH-一致, MISMATCH-不一致, MISSING_LEDGER-缺少台账, MISSING_BILL-缺少账单)

    @ApiModelProperty(value = "佣金差额")
    private BigDecimal commissionDifference; // 佣金差额 (台账佣金 - 账单佣金)
}
