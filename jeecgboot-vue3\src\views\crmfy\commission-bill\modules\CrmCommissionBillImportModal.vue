<template>
  <BasicModal v-bind="$attrs" @register="registerModal" title="导入保险公司佣金账单" @ok="handleSubmit">
    <BasicForm @register="registerForm" />
  </BasicModal>
</template>

<script lang="ts" setup>
  import { ref } from 'vue';
  import { BasicModal, useModalInner } from '/@/components/Modal';
  import { BasicForm, useForm } from '/@/components/Form/index';
  import { importCompanyBill } from '../CrmCommissionBill.api';
  import { useMessage } from '/@/hooks/web/useMessage';

  const { createMessage } = useMessage();

  // Emits声明
  const emit = defineEmits(['success', 'register']);

  //表单配置
  const [registerForm, { resetFields, validate }] = useForm({
    labelWidth: 150,
    schemas: [
      {
        label: '公司编码',
        field: 'companyCode',
        component: 'JDictSelectTag',
        componentProps: {
          dictCode: 'supplier',
          placeholder: '请选择保险公司',
        },
        required: true,
      },
      {
        label: '账单文件',
        field: 'file',
        component: 'Upload',
        componentProps: {
          accept: '.xlsx,.xls,.pdf',
          maxCount: 1,
          beforeUpload: () => false, // 阻止自动上传
          listType: 'text',
        },
        required: true,
      },
    ],
    showActionButtonGroup: false,
    baseColProps: { lg: 24, md: 24 },
  });

  //表单赋值
  const [registerModal, { setModalProps, closeModal }] = useModalInner(async (data) => {
    //重置表单
    await resetFields();
    setModalProps({ confirmLoading: false, showCancelBtn: !!data?.showFooter, showOkBtn: !!data?.showFooter });
  });

  //表单提交
  async function handleSubmit() {
    try {
      const values = await validate();
      setModalProps({ confirmLoading: true });

      if (!values.file || values.file.length === 0) {
        createMessage.error('请选择要导入的文件');
        return;
      }

      if (!values.companyCode) {
        createMessage.error('请选择保险公司');
        return;
      }

      const formData = new FormData();
      // 处理文件对象
      const fileObj = values.file[0];
      const file = fileObj.originFileObj || fileObj;
      formData.append('file', file);
      formData.append('companyCode', values.companyCode);

      //提交表单
      const result = await importCompanyBill(formData);

      if (result.success) {
        createMessage.success(result.message || '导入成功');
        //关闭弹窗
        closeModal();
        //刷新列表
        emit('success');
      } else {
        createMessage.error(result.message || '导入失败');
      }
    } catch (error) {
      createMessage.error('导入失败: ' + error.message);
    } finally {
      setModalProps({ confirmLoading: false });
    }
  }
</script>
