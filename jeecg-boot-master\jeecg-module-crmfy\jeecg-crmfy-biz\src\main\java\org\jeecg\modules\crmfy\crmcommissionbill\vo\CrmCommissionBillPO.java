package org.jeecg.modules.crmfy.crmcommissionbill.vo;

import java.io.Serializable;

import org.jeecg.common.aspect.annotation.Dict;
import org.jeecgframework.poi.excel.annotation.Excel;


import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class CrmCommissionBillPO implements Serializable{

    private static final long serialVersionUID = 1L;


	/**保单号*/
	@Excel(name = "保单号", width = 15)
    @ApiModelProperty(value = "保单号")
	private java.lang.String policyNo;

        
	@ApiModelProperty(value = "佣金期数")
	private java.lang.Integer yearNum;
	
    
	/**公司编码*/
	@Dict(dicCode = "supplier")
	@Excel(name = "公司编码", width = 15, dicCode = "supplier")
    @ApiModelProperty(value = "公司编码")
	private java.lang.String companyCode;



  
    @ApiModelProperty(value = "产品英文名称")
    private String planName; // 产品英文名称



}
