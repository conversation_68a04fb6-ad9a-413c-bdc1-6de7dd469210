import { BasicColumn } from '/@/components/Table';
import { FormSchema } from '/@/components/Table';
import { render } from '/@/utils/common/renderUtils';
import { Tag } from 'ant-design-vue';
import { h } from 'vue';

export const columns: BasicColumn[] = [
  {
    title: '保单号',
    dataIndex: 'policyNo',
    width: 150,
    fixed: 'left',
  },
  {
    title: '公司编码',
    dataIndex: 'companyCode',
    width: 120,
    customRender: ({ text }) => {
      return render.renderDict(text, 'supplier');
    },
  },
  {
    title: '产品英文名称',
    dataIndex: 'planName',
    width: 180,
  },
  {
    title: '佣金期数',
    dataIndex: 'yearNum',
    width: 100,
  },
  {
    title: '缴费币种',
    dataIndex: 'paymentCurrency',
    width: 100,
  },
  // 公司台账信息
  {
    title: '台账保费金额',
    dataIndex: 'ledgerPremiumAmount',
    width: 130,
    customRender: ({ text }) => {
      return render.renderMoney(text);
    },
  },
  {
    title: '台账佣金金额',
    dataIndex: 'ledgerCommissionAmount',
    width: 130,
    customRender: ({ text }) => {
      return render.renderMoney(text);
    },
  },
  {
    title: '台账计算日期',
    dataIndex: 'ledgerCalculationDate',
    width: 120,
    customRender: ({ text }) => {
      return render.renderDate(text);
    },
  },
  // 保险公司账单信息
  {
    title: '保单生效日期',
    dataIndex: 'inforceDate',
    width: 120,
    customRender: ({ text }) => {
      return render.renderDate(text);
    },
  },
  {
    title: '账单保费金额',
    dataIndex: 'billPremiumAmount',
    width: 130,
    customRender: ({ text }) => {
      return render.renderMoney(text);
    },
  },
  {
    title: '账单佣金金额',
    dataIndex: 'billCommissionAmount',
    width: 130,
    customRender: ({ text }) => {
      return render.renderMoney(text);
    },
  },
  {
    title: '导入时间',
    dataIndex: 'importTime',
    width: 150,
    customRender: ({ text }) => {
      return render.renderDate(text, 'YYYY-MM-DD HH:mm:ss');
    },
  },
  // 对账状态
  {
    title: '对账状态',
    dataIndex: 'reconcileStatus',
    width: 120,
    customRender: ({ text }) => {
      const statusMap = {
        'MATCH': { color: 'green', text: '一致' },
        'MISMATCH': { color: 'red', text: '不一致' },
        'MISSING_LEDGER': { color: 'orange', text: '缺少台账' },
        'MISSING_BILL': { color: 'orange', text: '缺少账单' },
      };
      const status = statusMap[text] || { color: 'default', text: text };
      return h(Tag, { color: status.color }, () => status.text);
    },
  },
  {
    title: '佣金差额',
    dataIndex: 'commissionDifference',
    width: 120,
    customRender: ({ text }) => {
      if (text === null || text === undefined) return '-';
      const value = parseFloat(text);
      const color = value === 0 ? 'green' : value > 0 ? 'blue' : 'red';
      return h('span', { style: { color } }, render.renderMoney(text));
    },
  },
];

export const searchFormSchema: FormSchema[] = [
  {
    label: '保单号',
    field: 'policyNo',
    component: 'Input',
    componentProps: {
      placeholder: '请输入保单号',
    },
    colProps: { span: 6 },
  },
  {
    label: '公司编码',
    field: 'companyCode',
    component: 'JDictSelectTag',
    componentProps: {
      dictCode: 'supplier',
      placeholder: '请选择公司',
    },
    colProps: { span: 6 },
  },
  {
    label: '佣金期数',
    field: 'yearNum',
    component: 'InputNumber',
    componentProps: {
      placeholder: '请输入佣金期数',
      min: 1,
    },
    colProps: { span: 6 },
  },
  {
    label: '产品英文名称',
    field: 'planName',
    component: 'Input',
    componentProps: {
      placeholder: '请输入产品英文名称',
    },
    colProps: { span: 6 },
  },
];
