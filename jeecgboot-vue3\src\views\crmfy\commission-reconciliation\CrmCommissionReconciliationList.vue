<template>
  <div>
    <!--引用表格-->
    <BasicTable @register="registerTable">
      <!--插槽:table标题-->
      <template #tableTitle>
        <a-button type="primary" preIcon="ant-design:export-outlined" @click="onExportXls">导出</a-button>
        <a-alert 
          message="对账说明" 
          description="黄色背景表示对账不一致，需要核查。左侧为公司台账数据，右侧为保险公司账单数据。" 
          type="info" 
          show-icon 
          style="margin-left: 16px; flex: 1;"
        />
      </template>
    </BasicTable>
  </div>
</template>

<script lang="ts" name="commission-reconciliation" setup>
  import { BasicTable, useTable } from '/@/components/Table';
  import { useListPage } from '/@/hooks/system/useListPage';
  import { columns, searchFormSchema } from './CrmCommissionReconciliation.data';
  import { queryBillCompare, getExportUrl } from '../commission-bill/CrmCommissionBill.api';

  //注册table数据
  const { prefixCls, tableContext, onExportXls } = useListPage({
    tableProps: {
      title: '佣金对账查询',
      api: queryBillCompare,
      columns,
      canResize: false,
      formConfig: {
        labelWidth: 120,
        schemas: searchFormSchema,
        autoSubmitOnEnter: true,
        showAdvancedButton: true,
      },
      rowClassName: (record) => {
        // 对账不一致的行标黄
        if (record.reconcileStatus === 'MISMATCH') {
          return 'reconcile-mismatch';
        }
        return '';
      },
    },
    exportConfig: {
      name: '佣金对账查询',
      url: getExportUrl,
    },
  });

  const [registerTable, { reload }] = tableContext;
</script>

<style scoped>
:deep(.reconcile-mismatch) {
  background-color: #fff7e6 !important;
}

:deep(.reconcile-mismatch:hover) {
  background-color: #fff1d6 !important;
}
</style>
