package org.jeecg.modules.crmfy.crmcommissionbill.service.impl;

import org.jeecg.modules.crmfy.crmcommissionbill.entity.CrmCommissionBill;
import org.jeecg.modules.crmfy.crmcommissionbill.mapper.CrmCommissionBillMapper;
import org.jeecg.modules.crmfy.crmcommissionbill.service.ICrmCommissionBillService;
import org.jeecg.modules.crmfy.crmcommissionbill.vo.CrmCommissionBillPO;
import org.jeecg.modules.crmfy.crmcommissionbill.vo.CrmCommissionBillVO;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

/**
 * @Description: 佣金账单
 * @Author: jeecg-boot
 * @Date:   2025-04-11
 * @Version: V1.0
 */
@Service
public class CrmCommissionBillServiceImpl extends ServiceImpl<CrmCommissionBillMapper, CrmCommissionBill> implements ICrmCommissionBillService {

    @Override
    public IPage<CrmCommissionBillVO> queryBillCompare(Page<CrmCommissionBillVO> page,
            CrmCommissionBillPO crmCommissionBill) {
        return this.baseMapper.queryBillCompare(page, crmCommissionBill);
    }

}
