# 佣金对账模块

## 功能概述

本模块实现了保险公司佣金对账功能，包括以下三个主要功能：

### 4.1 保险公司佣金账单导入
- **功能描述**: 导入各保险公司佣金对账单
- **输入**: 公司编号、账单文件（支持Excel和PDF格式）
- **输出**: 保险公司佣金账单入库到 `crm_commission_bill` 表
- **接口**: `/crmcommissionbill/crmCommissionBill/importCompanyBill`

### 4.2 保险公司账单查询
- **功能描述**: 分页查询导入的保险公司账单
- **输入**: 公司、保单号、产品英文名称、佣金期数
- **输出**: 保险公司账单列表，包含：
  - 保单号
  - 保单生效日期
  - 产品英文名称
  - 佣金期数
  - 缴费币种
  - 保费金额
  - 佣金金额
  - 导入时间
- **接口**: `/crmcommissionbill/crmCommissionBill/list`

### 4.3 佣金对账查询
- **功能描述**: 分页查询佣金账单对账列表，对比公司台账与保险公司账单
- **输入**: 保单号、公司、佣金期数
- **输出**: 佣金账单对账分页列表，包含：
  - 左边：公司台账信息（来自 `crm_commission_calculation`）
  - 右边：保险公司账单信息（来自 `crm_commission_bill`）
  - 对账状态：一致/不一致/缺少台账/缺少账单
  - 对账不一致的记录标黄显示
- **接口**: `/crmcommissionbill/crmCommissionBill/queryBillCompare`

## 技术实现

### 后端实现

#### 1. 实体类和VO
- `CrmCommissionBill`: 佣金账单实体类
- `CrmCommissionBillVO`: 对账查询结果VO，包含台账和账单对比信息
- `CrmCommissionBillPO`: 查询参数对象

#### 2. 服务层
- `ICrmCommissionBillService`: 服务接口
- `CrmCommissionBillServiceImpl`: 服务实现类
- `queryBillCompare`: 对账查询方法实现

#### 3. 数据访问层
- `CrmCommissionBillMapper`: MyBatis映射接口
- `CrmCommissionBillMapper.xml`: SQL映射文件，实现复杂的对账查询逻辑

#### 4. 控制器
- `CrmCommissionBillController`: REST API控制器
- 提供账单导入、查询、对账等接口

### 前端实现

#### 1. 页面组件
- `CrmCommissionBillList.vue`: 保险公司账单管理页面
- `CrmCommissionReconciliationList.vue`: 佣金对账查询页面
- `index.vue`: 主页面，使用Tab切换不同功能

#### 2. 模态框组件
- `CrmCommissionBillModal.vue`: 账单编辑模态框
- `CrmCommissionBillImportModal.vue`: 账单导入模态框

#### 3. 配置文件
- `CrmCommissionBill.data.ts`: 账单管理页面的表格列和表单配置
- `CrmCommissionReconciliation.data.ts`: 对账查询页面的表格列和表单配置
- `CrmCommissionBill.api.ts`: API接口定义

## 对账逻辑

### 对账状态说明
- **MATCH**: 佣金金额一致（差额小于0.01）
- **MISMATCH**: 佣金金额不一致
- **MISSING_LEDGER**: 保险公司有账单但公司台账中没有对应记录
- **MISSING_BILL**: 公司台账有记录但保险公司没有对应账单

### 对账规则
1. 按保单号和佣金期数进行匹配
2. 比较佣金金额，差额小于0.01认为一致
3. 不一致的记录在前端标黄显示
4. 支持按公司、保单号、佣金期数、产品名称进行筛选

## 使用说明

### 1. 导入保险公司账单
1. 进入"保险公司账单管理"页面
2. 点击"导入保险公司账单"按钮
3. 选择保险公司和账单文件
4. 点击确定完成导入

### 2. 查看账单列表
1. 在"保险公司账单管理"页面查看已导入的账单
2. 支持按公司、保单号等条件筛选
3. 可以编辑或删除账单记录

### 3. 进行佣金对账
1. 切换到"佣金对账查询"页面
2. 设置查询条件（可选）
3. 查看对账结果，黄色背景表示不一致
4. 可以导出对账结果

## 部署指南

### 1. 后端部署
确保以下文件已正确部署到后端项目：
- `CrmCommissionBillVO.java` - 对账查询结果VO
- `CrmCommissionBillMapper.java` - 数据访问接口
- `CrmCommissionBillMapper.xml` - SQL映射文件
- `CrmCommissionBillServiceImpl.java` - 服务实现类
- `CrmCommissionBillController.java` - 控制器（已存在，无需修改）

### 2. 前端部署
将以下文件复制到前端项目对应目录：
```
src/views/crmfy/
├── commission/
│   ├── index.vue                    # 主页面
│   ├── test.vue                     # 测试页面
│   ├── README.md                    # 文档
│   └── routes.example.ts            # 路由配置示例
├── commission-bill/
│   ├── CrmCommissionBillList.vue    # 账单管理页面
│   ├── CrmCommissionBill.data.ts    # 数据配置
│   ├── CrmCommissionBill.api.ts     # API接口
│   └── modules/
│       ├── CrmCommissionBillModal.vue       # 编辑模态框
│       └── CrmCommissionBillImportModal.vue # 导入模态框
└── commission-reconciliation/
    ├── CrmCommissionReconciliationList.vue  # 对账查询页面
    └── CrmCommissionReconciliation.data.ts  # 数据配置
```

### 3. 渲染工具函数
确保 `src/utils/common/renderUtils.ts` 文件包含了 `renderDate` 和 `renderMoney` 方法。

### 4. 路由配置
参考 `routes.example.ts` 文件，将路由配置添加到您的路由文件中。

### 5. 菜单配置
在系统管理中添加相应的菜单项，或通过数据库直接插入菜单数据。

## 测试指南

### 1. 功能测试
访问测试页面 `/crmfy/commission/test` 进行功能验证：
- 渲染函数测试
- API接口测试
- 组件功能测试

### 2. API测试
使用Postman或其他工具测试以下接口：
- `GET /crmcommissionbill/crmCommissionBill/list` - 账单列表
- `POST /crmcommissionbill/crmCommissionBill/importCompanyBill` - 导入账单
- `GET /crmcommissionbill/crmCommissionBill/queryBillCompare` - 对账查询

### 3. 数据库测试
确保以下表存在且结构正确：
- `crm_commission_bill` - 保险公司账单表
- `crm_commission_calculation` - 公司台账表

## 故障排除

### 1. 渲染函数报错
如果遇到 `renderDate` 或 `renderMoney` 未定义错误：
- 检查 `renderUtils.ts` 文件是否包含这些方法
- 确认 dayjs 依赖已正确安装

### 2. 文件上传失败
如果文件上传不成功：
- 检查后端导入接口是否正常工作
- 确认文件格式是否支持（.xlsx, .xls, .pdf）
- 检查文件大小限制

### 3. 对账查询无数据
如果对账查询返回空结果：
- 检查数据库中是否有测试数据
- 确认SQL查询语句是否正确执行
- 检查查询条件是否过于严格

### 4. 样式问题
如果页面样式异常：
- 检查CSS类名是否正确
- 确认Ant Design Vue组件版本兼容性
- 检查响应式布局配置

## 注意事项

1. 导入的账单文件支持Excel（.xlsx, .xls）和PDF格式
2. 对账查询使用UNION实现全外连接，兼容MySQL数据库
3. 前端使用响应式设计，支持不同屏幕尺寸
4. 所有金额字段支持小数点后两位精度
5. 删除操作为逻辑删除，通过del_flag字段控制
6. 建议在生产环境部署前进行充分测试
7. 确保数据库备份，避免数据丢失
