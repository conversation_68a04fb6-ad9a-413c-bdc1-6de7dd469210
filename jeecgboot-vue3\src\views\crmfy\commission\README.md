# 佣金对账模块

## 功能概述

本模块实现了保险公司佣金对账功能，包括以下三个主要功能：

### 4.1 保险公司佣金账单导入
- **功能描述**: 导入各保险公司佣金对账单
- **输入**: 公司编号、账单文件（支持Excel和PDF格式）
- **输出**: 保险公司佣金账单入库到 `crm_commission_bill` 表
- **接口**: `/crmcommissionbill/crmCommissionBill/importCompanyBill`

### 4.2 保险公司账单查询
- **功能描述**: 分页查询导入的保险公司账单
- **输入**: 公司、保单号、产品英文名称、佣金期数
- **输出**: 保险公司账单列表，包含：
  - 保单号
  - 保单生效日期
  - 产品英文名称
  - 佣金期数
  - 缴费币种
  - 保费金额
  - 佣金金额
  - 导入时间
- **接口**: `/crmcommissionbill/crmCommissionBill/list`

### 4.3 佣金对账查询
- **功能描述**: 分页查询佣金账单对账列表，对比公司台账与保险公司账单
- **输入**: 保单号、公司、佣金期数
- **输出**: 佣金账单对账分页列表，包含：
  - 左边：公司台账信息（来自 `crm_commission_calculation`）
  - 右边：保险公司账单信息（来自 `crm_commission_bill`）
  - 对账状态：一致/不一致/缺少台账/缺少账单
  - 对账不一致的记录标黄显示
- **接口**: `/crmcommissionbill/crmCommissionBill/queryBillCompare`

## 技术实现

### 后端实现

#### 1. 实体类和VO
- `CrmCommissionBill`: 佣金账单实体类
- `CrmCommissionBillVO`: 对账查询结果VO，包含台账和账单对比信息
- `CrmCommissionBillPO`: 查询参数对象

#### 2. 服务层
- `ICrmCommissionBillService`: 服务接口
- `CrmCommissionBillServiceImpl`: 服务实现类
- `queryBillCompare`: 对账查询方法实现

#### 3. 数据访问层
- `CrmCommissionBillMapper`: MyBatis映射接口
- `CrmCommissionBillMapper.xml`: SQL映射文件，实现复杂的对账查询逻辑

#### 4. 控制器
- `CrmCommissionBillController`: REST API控制器
- 提供账单导入、查询、对账等接口

### 前端实现

#### 1. 页面组件
- `CrmCommissionBillList.vue`: 保险公司账单管理页面
- `CrmCommissionReconciliationList.vue`: 佣金对账查询页面
- `index.vue`: 主页面，使用Tab切换不同功能

#### 2. 模态框组件
- `CrmCommissionBillModal.vue`: 账单编辑模态框
- `CrmCommissionBillImportModal.vue`: 账单导入模态框

#### 3. 配置文件
- `CrmCommissionBill.data.ts`: 账单管理页面的表格列和表单配置
- `CrmCommissionReconciliation.data.ts`: 对账查询页面的表格列和表单配置
- `CrmCommissionBill.api.ts`: API接口定义

## 对账逻辑

### 对账状态说明
- **MATCH**: 佣金金额一致（差额小于0.01）
- **MISMATCH**: 佣金金额不一致
- **MISSING_LEDGER**: 保险公司有账单但公司台账中没有对应记录
- **MISSING_BILL**: 公司台账有记录但保险公司没有对应账单

### 对账规则
1. 按保单号和佣金期数进行匹配
2. 比较佣金金额，差额小于0.01认为一致
3. 不一致的记录在前端标黄显示
4. 支持按公司、保单号、佣金期数、产品名称进行筛选

## 使用说明

### 1. 导入保险公司账单
1. 进入"保险公司账单管理"页面
2. 点击"导入保险公司账单"按钮
3. 选择保险公司和账单文件
4. 点击确定完成导入

### 2. 查看账单列表
1. 在"保险公司账单管理"页面查看已导入的账单
2. 支持按公司、保单号等条件筛选
3. 可以编辑或删除账单记录

### 3. 进行佣金对账
1. 切换到"佣金对账查询"页面
2. 设置查询条件（可选）
3. 查看对账结果，黄色背景表示不一致
4. 可以导出对账结果

## 注意事项

1. 导入的账单文件支持Excel（.xlsx, .xls）和PDF格式
2. 对账查询使用UNION实现全外连接，兼容MySQL数据库
3. 前端使用响应式设计，支持不同屏幕尺寸
4. 所有金额字段支持小数点后两位精度
5. 删除操作为逻辑删除，通过del_flag字段控制
