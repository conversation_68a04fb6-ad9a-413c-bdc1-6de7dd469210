<template>
  <div class="p-4">
    <a-card title="佣金对账模块测试" :bordered="false">
      <a-space direction="vertical" size="large" style="width: 100%">

        <!-- 渲染函数测试 -->
        <a-card title="渲染函数测试" size="small">
          <a-row :gutter="16">
            <a-col :span="8">
              <p>日期渲染测试:</p>
              <div>{{ renderDateTest }}</div>
            </a-col>
            <a-col :span="8">
              <p>金额渲染测试:</p>
              <div>{{ renderMoneyTest }}</div>
            </a-col>
            <a-col :span="8">
              <p>字典渲染测试:</p>
              <div>{{ renderDictTest }}</div>
            </a-col>
          </a-row>
        </a-card>

        <!-- API 测试 -->
        <a-card title="API 接口测试" size="small">
          <a-space>
            <a-button type="primary" @click="testBillListApi">测试账单列表API</a-button>
            <a-button type="primary" @click="testReconciliationApi">测试对账查询API</a-button>
            <a-button type="default" @click="clearResults">清空结果</a-button>
          </a-space>

          <div v-if="apiResults" style="margin-top: 16px">
            <a-alert
              :message="apiResults.title"
              :description="apiResults.message"
              :type="apiResults.type"
              show-icon
            />
            <pre v-if="apiResults.data" style="margin-top: 8px; background: #f5f5f5; padding: 8px; border-radius: 4px;">{{ JSON.stringify(apiResults.data, null, 2) }}</pre>
          </div>
        </a-card>

        <!-- 组件测试 -->
        <a-card title="组件功能测试" size="small">
          <a-space>
            <a-button type="primary" @click="testImportModal">测试导入模态框</a-button>
            <a-button type="primary" @click="testEditModal">测试编辑模态框</a-button>
          </a-space>

          <div style="margin-top: 16px;">
            <a-alert
              message="测试说明"
              description="点击按钮测试各个组件功能。导入模态框现在使用原生Upload组件，支持multipart/form-data文件上传。"
              type="info"
              show-icon
            />
          </div>
        </a-card>

      </a-space>
    </a-card>

    <!-- 导入模态框 -->
    <CrmCommissionBillImportModal @register="registerImportModal" @success="handleSuccess" />
    <!-- 编辑模态框 -->
    <CrmCommissionBillModal @register="registerEditModal" @success="handleSuccess" />
  </div>
</template>

<script lang="ts" setup>
  import { ref, computed } from 'vue';
  import { useModal } from '/@/components/Modal';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { render } from '/@/utils/common/renderUtils';
  import { list, queryBillCompare } from '../commission-bill/CrmCommissionBill.api';
  import CrmCommissionBillImportModal from '../commission-bill/modules/CrmCommissionBillImportModal.vue';
  import CrmCommissionBillModal from '../commission-bill/modules/CrmCommissionBillModal.vue';

  const { createMessage } = useMessage();

  // 注册模态框
  const [registerImportModal, { openModal: openImportModal }] = useModal();
  const [registerEditModal, { openModal: openEditModal }] = useModal();

  // API 测试结果
  const apiResults = ref(null);

  // 渲染函数测试
  const renderDateTest = computed(() => {
    const testDate = new Date();
    return render.renderDate(testDate, 'YYYY-MM-DD HH:mm:ss');
  });

  const renderMoneyTest = computed(() => {
    return render.renderMoney(12345.67, '$');
  });

  const renderDictTest = computed(() => {
    return render.renderDict('test_value', 'supplier');
  });

  // 测试账单列表API
  async function testBillListApi() {
    try {
      const result = await list({ pageNo: 1, pageSize: 10 });
      apiResults.value = {
        title: '账单列表API测试',
        type: 'success',
        message: '请求成功',
        data: result
      };
    } catch (error) {
      apiResults.value = {
        title: '账单列表API测试',
        type: 'error',
        message: '请求失败: ' + error.message,
        data: null
      };
    }
  }

  // 测试对账查询API
  async function testReconciliationApi() {
    try {
      const result = await queryBillCompare({ pageNo: 1, pageSize: 10 });
      apiResults.value = {
        title: '对账查询API测试',
        type: 'success',
        message: '请求成功',
        data: result
      };
    } catch (error) {
      apiResults.value = {
        title: '对账查询API测试',
        type: 'error',
        message: '请求失败: ' + error.message,
        data: null
      };
    }
  }

  // 清空结果
  function clearResults() {
    apiResults.value = null;
  }

  // 测试导入模态框
  function testImportModal() {
    openImportModal(true, {
      isUpdate: false,
      showFooter: true,
    });
  }

  // 测试编辑模态框
  function testEditModal() {
    openEditModal(true, {
      record: {
        id: 'test',
        policyNo: 'TEST001',
        companyCode: 'AIA',
        planName: 'Test Plan',
        yearNum: 1,
        premiumAmount: 10000,
        commissionAmount: 500,
      },
      isUpdate: true,
      showFooter: true,
    });
  }

  // 成功回调
  function handleSuccess() {
    createMessage.success('操作成功');
  }
</script>

<style scoped>
pre {
  max-height: 300px;
  overflow-y: auto;
}
</style>
