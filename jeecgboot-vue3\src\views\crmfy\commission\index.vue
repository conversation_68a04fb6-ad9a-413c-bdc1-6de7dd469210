<template>
  <div class="p-4">
    <a-card :bordered="false" style="height: 100%">
      <a-tabs v-model:activeKey="activeKey" @change="tabChange">
        <a-tab-pane key="bill-list" tab="保险公司账单管理"></a-tab-pane>
        <a-tab-pane key="reconciliation" tab="佣金对账查询"></a-tab-pane>
      </a-tabs>
      <component :is="currentComponent"></component>
    </a-card>
  </div>
</template>

<script lang="ts">
  import { defineComponent, ref, computed } from 'vue';
  import CrmCommissionBillList from '../commission-bill/CrmCommissionBillList.vue';
  import CrmCommissionReconciliationList from '../commission-reconciliation/CrmCommissionReconciliationList.vue';

  export default defineComponent({
    name: 'CommissionManagement',
    setup() {
      const activeKey = ref('bill-list');
      const currentComponent = computed(() => {
        const componentType = {
          'bill-list': CrmCommissionBillList,
          'reconciliation': CrmCommissionReconciliationList,
        };
        return componentType[activeKey.value];
      });

      //使用component动态切换tab
      function tabChange(key) {
        activeKey.value = key;
      }

      return {
        activeKey,
        currentComponent,
        tabChange,
      };
    },
  });
</script>

<style scoped></style>
