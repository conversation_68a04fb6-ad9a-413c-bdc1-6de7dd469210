package org.jeecg.modules.crmfy.crmcommissionbill.entity;

import java.io.Serializable;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import org.jeecg.common.aspect.annotation.Dict;
import org.jeecgframework.poi.excel.annotation.Excel;

/**
 * @Description: 佣金账单
 * @Author: jeecg-boot
 * @Date:   2025-04-11
 * @Version: V1.0
 */
@Data
@TableName("crm_commission_bill")
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="crm_commission_bill对象", description="佣金账单")
public class CrmCommissionBill {
    
	/**ID*/
	@TableId(type = IdType.AUTO)
    @ApiModelProperty(value = "ID")
	private java.lang.Integer id;

	@ApiModelProperty(value = "佣金计算ID")
	private java.lang.Integer commissionId;

	@ApiModelProperty(value = "佣金期数")
	private java.lang.Integer yearNum;
	
	/**保单号*/
	@Excel(name = "保单号", width = 15)
    @ApiModelProperty(value = "保单号")
	private java.lang.String policyNo;
	/**公司编码*/
	@Dict(dicCode = "supplier")
	@Excel(name = "公司编码", width = 15, dicCode = "supplier")
    @ApiModelProperty(value = "公司编码")
	private java.lang.String companyCode;
	/**保单生效日期*/
	@Excel(name = "保单生效日期", width = 15, format = "yyyy-MM-dd")
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @ApiModelProperty(value = "保单生效日期")
	private java.util.Date inforceDate;
	/**佣金处理日期*/
	@Excel(name = "佣金处理日期", width = 15, format = "yyyy-MM-dd")
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @ApiModelProperty(value = "佣金处理日期")
	private java.util.Date transactionDate;
	/**产品英文名称*/
	@Excel(name = "产品英文名称", width = 15)
    @ApiModelProperty(value = "产品英文名称")
	private java.lang.String planName;
	/**产品编码*/
	@Excel(name = "产品编码", width = 15)
    @ApiModelProperty(value = "产品编码")
	private java.lang.String planCode;
	/**缴费币种*/
	@Excel(name = "缴费币种", width = 15)
    @ApiModelProperty(value = "缴费币种")
	private java.lang.String paymentCurrency;
	/**保费金额*/
	@Excel(name = "保费金额", width = 15)
    @ApiModelProperty(value = "保费金额")
	private java.math.BigDecimal premiumAmount;
	/**佣金率*/
	@Excel(name = "佣金率", width = 15)
    @ApiModelProperty(value = "佣金率")
	private java.math.BigDecimal commissionRate;
	/**佣金金额*/
	@Excel(name = "佣金金额", width = 15)
    @ApiModelProperty(value = "佣金金额")
	private java.math.BigDecimal commissionAmount;
	/**首年保费金额*/
	@Excel(name = "首年保费金额", width = 15)
    @ApiModelProperty(value = "首年保费金额")
	private java.math.BigDecimal fyp;
	/**续期保费金额*/
	@Excel(name = "续期保费金额", width = 15)
    @ApiModelProperty(value = "续期保费金额")
	private java.math.BigDecimal rp;
	/**续期佣金金额*/
	@Excel(name = "续期佣金金额", width = 15)
    @ApiModelProperty(value = "续期佣金金额")
	private java.math.BigDecimal rc;
	/**标准佣金金额*/
	@Excel(name = "标准佣金金额", width = 15)
    @ApiModelProperty(value = "标准佣金金额")
	private java.math.BigDecimal afyc;
	/**首年佣金金额*/
	@Excel(name = "首年佣金金额", width = 15)
    @ApiModelProperty(value = "首年佣金金额")
	private java.math.BigDecimal fyc;
	/**汇率*/
	@Excel(name = "汇率", width = 15)
    @ApiModelProperty(value = "汇率")
	private java.math.BigDecimal rcExchangeRate;
	/**部门*/
    @ApiModelProperty(value = "部门")
	private java.lang.String sysOrgCode;
	/**创建人*/
    @ApiModelProperty(value = "创建人")
	private java.lang.String createBy;
	/**创建时间*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建时间")
	private java.util.Date createTime;
	/**修改人*/
    @ApiModelProperty(value = "修改人")
	private java.lang.String updateBy;
	/**修改时间*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "修改时间")
	private java.util.Date updateTime;
	/**0表示未删除,1表示删除*/
    @ApiModelProperty(value = "0表示未删除,1表示删除")
	private java.lang.Integer delFlag;
	/**租户ID*/
    @ApiModelProperty(value = "租户ID")
	private java.lang.Integer tenantId;
	/**销售员username*/
	// @Excel(name = "销售员username", width = 15)
    // @ApiModelProperty(value = "销售员username")
	// private java.lang.String saleName;
}
