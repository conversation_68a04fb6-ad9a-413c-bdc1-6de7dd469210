// 佣金对账模块路由配置示例
// 请将以下路由配置添加到您的路由文件中

export const commissionRoutes = [
  {
    path: '/crmfy/commission',
    name: 'CommissionManagement',
    component: () => import('/@/views/crmfy/commission/index.vue'),
    meta: {
      title: '佣金管理',
      icon: 'ant-design:account-book-outlined',
    },
    children: [
      {
        path: 'bill-list',
        name: 'CommissionBillList',
        component: () => import('/@/views/crmfy/commission-bill/CrmCommissionBillList.vue'),
        meta: {
          title: '保险公司账单管理',
          keepAlive: true,
        },
      },
      {
        path: 'reconciliation',
        name: 'CommissionReconciliation',
        component: () => import('/@/views/crmfy/commission-reconciliation/CrmCommissionReconciliationList.vue'),
        meta: {
          title: '佣金对账查询',
          keepAlive: true,
        },
      },
      {
        path: 'test',
        name: 'CommissionTest',
        component: () => import('/@/views/crmfy/commission/test.vue'),
        meta: {
          title: '功能测试',
          hideMenu: true, // 隐藏菜单，仅用于测试
        },
      },
    ],
  },
];

// 或者作为独立的路由项
export const commissionStandaloneRoutes = [
  {
    path: '/crmfy/commission-bill',
    name: 'CommissionBillManagement',
    component: () => import('/@/views/crmfy/commission-bill/CrmCommissionBillList.vue'),
    meta: {
      title: '保险公司账单管理',
      icon: 'ant-design:file-text-outlined',
      keepAlive: true,
    },
  },
  {
    path: '/crmfy/commission-reconciliation',
    name: 'CommissionReconciliationManagement',
    component: () => import('/@/views/crmfy/commission-reconciliation/CrmCommissionReconciliationList.vue'),
    meta: {
      title: '佣金对账查询',
      icon: 'ant-design:diff-outlined',
      keepAlive: true,
    },
  },
];

// 菜单配置示例
export const commissionMenus = [
  {
    id: 'commission-management',
    parentId: 'crmfy',
    name: '佣金管理',
    url: '/crmfy/commission',
    component: 'views/crmfy/commission/index',
    icon: 'ant-design:account-book-outlined',
    orderNum: 10,
    menuType: 0, // 目录
    children: [
      {
        id: 'commission-bill-list',
        parentId: 'commission-management',
        name: '保险公司账单管理',
        url: '/crmfy/commission/bill-list',
        component: 'views/crmfy/commission-bill/CrmCommissionBillList',
        icon: 'ant-design:file-text-outlined',
        orderNum: 1,
        menuType: 1, // 菜单
        perms: 'crmfy:commission-bill:list',
      },
      {
        id: 'commission-reconciliation',
        parentId: 'commission-management',
        name: '佣金对账查询',
        url: '/crmfy/commission/reconciliation',
        component: 'views/crmfy/commission-reconciliation/CrmCommissionReconciliationList',
        icon: 'ant-design:diff-outlined',
        orderNum: 2,
        menuType: 1, // 菜单
        perms: 'crmfy:commission-reconciliation:list',
      },
    ],
  },
];

// 权限配置示例
export const commissionPermissions = [
  // 保险公司账单管理权限
  'crmfy:commission-bill:list',     // 查看列表
  'crmfy:commission-bill:add',      // 新增
  'crmfy:commission-bill:edit',     // 编辑
  'crmfy:commission-bill:delete',   // 删除
  'crmfy:commission-bill:import',   // 导入
  'crmfy:commission-bill:export',   // 导出
  
  // 佣金对账查询权限
  'crmfy:commission-reconciliation:list',   // 查看对账列表
  'crmfy:commission-reconciliation:export', // 导出对账结果
];
