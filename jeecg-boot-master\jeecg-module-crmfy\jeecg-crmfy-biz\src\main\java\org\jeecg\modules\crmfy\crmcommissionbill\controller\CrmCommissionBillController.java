package org.jeecg.modules.crmfy.crmcommissionbill.controller;

import java.io.File;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.apache.commons.io.FilenameUtils;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.jeecg.common.util.oConvertUtils;
import org.jeecg.modules.crmfy.crmcommissionbill.entity.CrmCommissionBill;
import org.jeecg.modules.crmfy.crmcommissionbill.service.ICrmCommissionBillImportService;
import org.jeecg.modules.crmfy.crmcommissionbill.service.ICrmCommissionBillService;
import org.jeecg.modules.crmfy.crmcommissionbill.service.IFileImportService;
import org.jeecg.modules.crmfy.crmcommissionbill.vo.CrmCommissionBillPO;
import org.jeecg.modules.crmfy.crmcommissionbill.vo.CrmCommissionBillVO;

import java.util.Date;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.system.base.controller.JeecgController;
import org.jeecgframework.poi.excel.ExcelImportUtil;
import org.jeecgframework.poi.excel.def.NormalExcelConstants;
import org.jeecgframework.poi.excel.entity.ExportParams;
import org.jeecgframework.poi.excel.entity.ImportParams;
import org.jeecgframework.poi.excel.view.JeecgEntityExcelView;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.servlet.ModelAndView;
import com.alibaba.fastjson.JSON;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

 /**
 * @Description: 佣金账单
 * @Author: jeecg-boot
 * @Date:   2025-04-11
 * @Version: V1.0
 */
@Slf4j
@Api(tags="佣金账单")
@RestController
@RequestMapping("/crmcommissionbill/crmCommissionBill")
public class CrmCommissionBillController extends JeecgController<CrmCommissionBill, ICrmCommissionBillService> {
	@Autowired
	private ICrmCommissionBillService crmCommissionBillService;

	@Autowired
	private ICrmCommissionBillImportService crmCommissionBillImportService;

	@Autowired
	private IFileImportService fileImportService;

	/**
	 * 分页列表查询
	 *
	 * @param crmCommissionBill
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	@AutoLog(value = "佣金账单-分页列表查询")
	@ApiOperation(value="佣金账单-分页列表查询", notes="佣金账单-分页列表查询")
	@GetMapping(value = "/list")
	public Result<IPage<CrmCommissionBill>> queryPageList(CrmCommissionBill crmCommissionBill,
								   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
								   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
								   HttpServletRequest req) {
		QueryWrapper<CrmCommissionBill> queryWrapper = QueryGenerator.initQueryWrapper(crmCommissionBill, req.getParameterMap());
		Page<CrmCommissionBill> page = new Page<CrmCommissionBill>(pageNo, pageSize);
		IPage<CrmCommissionBill> pageList = crmCommissionBillService.page(page, queryWrapper);
		return Result.OK(pageList);
	}


	@AutoLog(value = "佣金账单-对账查询")
	@ApiOperation(value="佣金账单-对账查询", notes="佣金账单-对账查询")
	@GetMapping(value = "/queryBillCompare")
	public Result<IPage<CrmCommissionBillVO>> queryBillCompare(CrmCommissionBillPO crmCommissionBill,
								   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
								   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
								   HttpServletRequest req) {
		// QueryWrapper<CrmCommissionBill> queryWrapper = QueryGenerator.initQueryWrapper(crmCommissionBill, req.getParameterMap());
		Page<CrmCommissionBillVO> page = new Page<CrmCommissionBillVO>(pageNo, pageSize);
		IPage<CrmCommissionBillVO> pageList = crmCommissionBillService.queryBillCompare(page, crmCommissionBill);
		return Result.OK(pageList);
	}

	/**
	 * 添加
	 *
	 * @param crmCommissionBill
	 * @return
	 */
	@AutoLog(value = "佣金账单-添加")
	@ApiOperation(value="佣金账单-添加", notes="佣金账单-添加")
	@PostMapping(value = "/add")
	public Result<?> add(@RequestBody CrmCommissionBill crmCommissionBill) {
		crmCommissionBillService.save(crmCommissionBill);
		return Result.OK("添加成功！");
	}

	/**
	 * 编辑
	 *
	 * @param crmCommissionBill
	 * @return
	 */
	@AutoLog(value = "佣金账单-编辑")
	@ApiOperation(value="佣金账单-编辑", notes="佣金账单-编辑")
	@RequestMapping(value = "/edit", method = RequestMethod.POST)
	public Result<?> edit(@RequestBody CrmCommissionBill crmCommissionBill) {
		crmCommissionBillService.updateById(crmCommissionBill);
		return Result.OK("编辑成功!");
	}

	/**
	 * 通过id删除
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "佣金账单-通过id删除")
	@ApiOperation(value="佣金账单-通过id删除", notes="佣金账单-通过id删除")
	@GetMapping(value = "/delete")
	public Result<?> delete(@RequestParam(name="id",required=true) String id) {
		crmCommissionBillService.removeById(id);
		return Result.OK("删除成功!");
	}

	/**
	 * 批量删除
	 *
	 * @param ids
	 * @return
	 */
	@AutoLog(value = "佣金账单-批量删除")
	@ApiOperation(value="佣金账单-批量删除", notes="佣金账单-批量删除")
	@GetMapping(value = "/deleteBatch")
	public Result<?> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		this.crmCommissionBillService.removeByIds(Arrays.asList(ids.split(",")));
		return Result.OK("批量删除成功！");
	}

	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "佣金账单-通过id查询")
	@ApiOperation(value="佣金账单-通过id查询", notes="佣金账单-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<?> queryById(@RequestParam(name="id",required=true) String id) {
		CrmCommissionBill crmCommissionBill = crmCommissionBillService.getById(id);
		return Result.OK(crmCommissionBill);
	}

  /**
   * 导出excel
   *
   * @param request
   * @param crmCommissionBill
   */
  @RequestMapping(value = "/exportXls")
  public ModelAndView exportXls(HttpServletRequest request, CrmCommissionBill crmCommissionBill) {
      return super.exportXls(request, crmCommissionBill, CrmCommissionBill.class, "佣金账单");
  }




//   /**
//    * 导入保险公司佣金对账单(Excel格式)
//    *
//    * @param file Excel文件
//    * @param companyCode 保险公司代码
//    * @return 导入结果
//    */
//  @AutoLog(value = "佣金账单-导入保险公司佣金对账单(Excel)")
//  @ApiOperation(value="佣金账单-导入保险公司佣金对账单(Excel)", notes="佣金账单-导入保险公司佣金对账单(Excel)")
//  @PostMapping(value = "/importCompanyExcel")
//  public Result<?> importCompanyExcel(@RequestParam("file") MultipartFile file,
//                                     @RequestParam("companyCode") String companyCode) {
//      return crmCommissionBillImportService.importFromExcel(file, companyCode);
//  }

  /**
   * 导入保险公司佣金对账单(PDF格式)
   *
   * @param file PDF文件
   * @param companyCode 保险公司代码
   * @return 导入结果
   */
  @AutoLog(value = "佣金账单-导入保险公司佣金对账单")
  @ApiOperation(value="佣金账单-导入保险公司佣金对账单", notes="佣金账单-导入保险公司佣金对账单")
  @PostMapping(value = "/importCompanyBill")
  public Result<?> importCompanyBill(@RequestParam("file") MultipartFile file,
                                   @RequestParam("companyCode") String companyCode) {
	  String fileName = file.getOriginalFilename();
	  String extension = FilenameUtils.getExtension(fileName).toLowerCase();
      Result<?> result;
      
      if (extension.equals("xlsx") || extension.equals("xls")) {
          result = crmCommissionBillImportService.importFromExcel(file, companyCode);
      } else if (extension.equals("pdf")) {
          result = crmCommissionBillImportService.importFromPdf(file, companyCode);
      } else {
          return Result.error("不支持的文件格式: " + extension);
      }
      return result;
  }



}
