import { defHttp } from '/@/utils/http/axios';
import { useMessage } from '/@/hooks/web/useMessage';

const { createConfirm } = useMessage();

enum Api {
  list = '/crmcommissionbill/crmCommissionBill/list',
  save = '/crmcommissionbill/crmCommissionBill/add',
  edit = '/crmcommissionbill/crmCommissionBill/edit',
  deleteOne = '/crmcommissionbill/crmCommissionBill/delete',
  deleteBatch = '/crmcommissionbill/crmCommissionBill/deleteBatch',
  importExcel = '/crmcommissionbill/crmCommissionBill/importExcel',
  exportXls = '/crmcommissionbill/crmCommissionBill/exportXls',
  importCompanyBill = '/crmcommissionbill/crmCommissionBill/importCompanyBill',
  queryBillCompare = '/crmcommissionbill/crmCommissionBill/queryBillCompare',
}

/**
 * 导出api
 */
export const getExportUrl = Api.exportXls;

/**
 * 导入api
 */
export const getImportUrl = Api.importExcel;

/**
 * 列表接口
 */
export const list = (params) => defHttp.get({ url: Api.list, params });

/**
 * 删除单个
 */
export const deleteOne = (params, handleSuccess) => {
  return defHttp.delete({ url: Api.deleteOne, params }, { joinParamsToUrl: true }).then(() => {
    handleSuccess();
  });
};

/**
 * 批量删除
 */
export const batchDelete = (params, handleSuccess) => {
  createConfirm({
    iconType: 'warning',
    title: '确认删除',
    content: '是否删除选中数据',
    okText: '确认',
    cancelText: '取消',
    onOk: () => {
      return defHttp.delete({ url: Api.deleteBatch, data: params }, { joinParamsToUrl: true }).then(() => {
        handleSuccess();
      });
    },
  });
};

/**
 * 保存或者更新
 */
export const saveOrUpdate = (params, isUpdate) => {
  const url = isUpdate ? Api.edit : Api.save;
  return defHttp.post({ url: url, params });
};

/**
 * 导入保险公司佣金账单
 */
export const importCompanyBill = (params: any) => {
  // 如果传入的是FormData，使用post方法上传文件
  if (params instanceof FormData) {
    return defHttp.post({
      url: Api.importCompanyBill,
      data: params,
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
  }
  // 如果传入的是普通对象（包含filePath），使用post方法
  return defHttp.post({
    url: Api.importCompanyBill,
    params: params,
  });
};

/**
 * 佣金对账查询
 */
export const queryBillCompare = (params: any) => defHttp.get({ url: Api.queryBillCompare, params });
